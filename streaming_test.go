package mux

import (
	"bytes"
	"io"
	"net"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/panjf2000/gnet/v2"
)

func TestStreamingBasic(t *testing.T) {
	engine := New()

	// Test basic streaming functionality
	engine.GET("/stream", func(c *Context) {
		c.<PERSON><PERSON>("Content-Type", "text/plain")

		counter := 0
		c.Stream(func(w io.Writer) bool {
			counter++
			if counter <= 3 {
				w.Write([]byte("chunk " + string(rune('0'+counter)) + "\n"))
				return true
			}
			return false
		})
	})

	// Create a test request
	req, err := http.NewRequest("GET", "/stream", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Create a mock connection for testing
	mockConn := &mockConnection{
		buffer: &bytes.Buffer{},
	}

	// Create context
	ctx := engine.createContext(mockConn, req)

	// Handle the request
	engine.handleRequest(ctx)

	// Check if streaming was enabled
	if !ctx.response.streaming {
		t.Error("Expected streaming to be enabled")
	}

	if !ctx.response.written {
		t.Error("Expected response to be written")
	}

	// Check if the response contains chunked encoding
	response := mockConn.buffer.String()
	if !strings.Contains(response, "Transfer-Encoding: chunked") {
		t.Error("Expected Transfer-Encoding: chunked header")
	}

	// Check if response contains chunks
	if !strings.Contains(response, "chunk 1") {
		t.Error("Expected to find 'chunk 1' in response")
	}

	if !strings.Contains(response, "chunk 2") {
		t.Error("Expected to find 'chunk 2' in response")
	}

	if !strings.Contains(response, "chunk 3") {
		t.Error("Expected to find 'chunk 3' in response")
	}

	// Check for end chunk
	if !strings.Contains(response, "0\r\n\r\n") {
		t.Error("Expected to find end chunk '0\\r\\n\\r\\n' in response")
	}
}

func TestStreamingHeaders(t *testing.T) {
	engine := New()

	engine.GET("/stream", func(c *Context) {
		c.Header("Content-Type", "text/event-stream")
		c.Header("Cache-Control", "no-cache")
		c.Status(200)

		c.Stream(func(w io.Writer) bool {
			w.Write([]byte("data: test\n\n"))
			return false // Stop after one write
		})
	})

	req, err := http.NewRequest("GET", "/stream", nil)
	if err != nil {
		t.Fatal(err)
	}

	mockConn := &mockConnection{
		buffer: &bytes.Buffer{},
	}

	ctx := engine.createContext(mockConn, req)
	engine.handleRequest(ctx)

	response := mockConn.buffer.String()

	// Check status code
	if !strings.Contains(response, "HTTP/1.1 200 OK") {
		t.Error("Expected HTTP/1.1 200 OK status")
	}

	// Check custom headers
	if !strings.Contains(response, "Content-Type: text/event-stream") {
		t.Error("Expected Content-Type: text/event-stream header")
	}

	if !strings.Contains(response, "Cache-Control: no-cache") {
		t.Error("Expected Cache-Control: no-cache header")
	}

	// Check chunked encoding
	if !strings.Contains(response, "Transfer-Encoding: chunked") {
		t.Error("Expected Transfer-Encoding: chunked header")
	}
}

func TestStreamingPreventDoubleWrite(t *testing.T) {
	engine := New()

	engine.GET("/stream", func(c *Context) {
		// Start streaming
		c.Stream(func(w io.Writer) bool {
			w.Write([]byte("streaming data"))
			return false
		})

		// Try to write regular response after streaming (should be ignored)
		c.JSON(200, map[string]string{"message": "this should be ignored"})
	})

	req, err := http.NewRequest("GET", "/stream", nil)
	if err != nil {
		t.Fatal(err)
	}

	mockConn := &mockConnection{
		buffer: &bytes.Buffer{},
	}

	ctx := engine.createContext(mockConn, req)
	engine.handleRequest(ctx)

	response := mockConn.buffer.String()

	// Should contain streaming data
	if !strings.Contains(response, "streaming data") {
		t.Error("Expected to find streaming data")
	}

	// Should NOT contain JSON response
	if strings.Contains(response, "this should be ignored") {
		t.Error("JSON response should be ignored after streaming started")
	}
}

// mockConnection implements gnet.Conn interface for testing
type mockConnection struct {
	buffer *bytes.Buffer
	ctx    interface{}
}

func (m *mockConnection) Read(p []byte) (n int, err error) {
	return 0, nil
}

func (m *mockConnection) Write(p []byte) (n int, err error) {
	return m.buffer.Write(p)
}

func (m *mockConnection) Close() error {
	return nil
}

func (m *mockConnection) LocalAddr() net.Addr {
	return nil
}

func (m *mockConnection) RemoteAddr() net.Addr {
	return nil
}

func (m *mockConnection) SetDeadline(t time.Time) error {
	return nil
}

func (m *mockConnection) SetReadDeadline(t time.Time) error {
	return nil
}

func (m *mockConnection) SetWriteDeadline(t time.Time) error {
	return nil
}

func (m *mockConnection) Context() interface{} {
	if m.ctx == nil {
		m.ctx = &httpCodec{
			parser: newHTTPParser(),
			buf:    make([]byte, 0),
		}
	}
	return m.ctx
}

func (m *mockConnection) SetContext(ctx interface{}) {
	m.ctx = ctx
}

// Additional gnet.Conn methods (simplified for testing)
func (m *mockConnection) Fd() int                                  { return 0 }
func (m *mockConnection) Dup() (int, error)                        { return 0, nil }
func (m *mockConnection) SetReadBuffer(bytes int) error            { return nil }
func (m *mockConnection) SetWriteBuffer(bytes int) error           { return nil }
func (m *mockConnection) SetLinger(sec int) error                  { return nil }
func (m *mockConnection) SetNoDelay(noDelay bool) error            { return nil }
func (m *mockConnection) SetKeepAlivePeriod(d time.Duration) error { return nil }
func (m *mockConnection) BufferLength() int                        { return 0 }
func (m *mockConnection) AsyncWrite(buf []byte, callback gnet.AsyncCallback) error {
	return nil
}

func (m *mockConnection) AsyncWritev(bs [][]byte, callback gnet.AsyncCallback) error {
	return nil
}
func (m *mockConnection) Writev(bs [][]byte) (n int, err error)     { return 0, nil }
func (m *mockConnection) Flush() error                              { return nil }
func (m *mockConnection) InboundBuffered() int                      { return 0 }
func (m *mockConnection) OutboundBuffered() int                     { return 0 }
func (m *mockConnection) Discard(n int) (discarded int, err error)  { return 0, nil }
func (m *mockConnection) Peek(n int) (buf []byte, err error)        { return nil, nil }
func (m *mockConnection) Next(n int) (buf []byte, err error)        { return nil, nil }
func (m *mockConnection) ReadFrom(r io.Reader) (n int64, err error) { return 0, nil }
func (m *mockConnection) WriteTo(w io.Writer) (n int64, err error)  { return 0, nil }
func (m *mockConnection) CloseWithCallback(callback gnet.AsyncCallback) error {
	return nil
}
func (m *mockConnection) SetDeadlineCallback(callback func() error) error      { return nil }
func (m *mockConnection) SetReadDeadlineCallback(callback func() error) error  { return nil }
func (m *mockConnection) SetWriteDeadlineCallback(callback func() error) error { return nil }
func (m *mockConnection) Wake(callback gnet.AsyncCallback) error               { return nil }
